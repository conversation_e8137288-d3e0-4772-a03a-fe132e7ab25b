from django import forms
from django.contrib.auth.models import User
from device_manager.models import Device
from fields.models import Field
from accounts.models import UserProfile
from .models import NotificationSettings


class DeviceSelectionWidget(forms.Widget):
    """Custom widget for device selection with filtering capabilities"""

    def __init__(self, attrs=None, user=None, show_all_devices=False):
        super().__init__(attrs)
        self.user = user
        self.show_all_devices = show_all_devices  # For admin forms that need to show all devices

    def render(self, name, value, attrs=None, renderer=None):
        if attrs is None:
            attrs = {}

        # Get user's accessible devices
        if self.show_all_devices:
            # For admin forms, show all devices
            devices = Device.objects.all().select_related('fild')
        elif self.user:
            if self.user.is_superuser:
                devices = Device.objects.all().select_related('fild')
            else:
                try:
                    user_profile = UserProfile.objects.get(user=self.user)
                    devices = user_profile.devs.all().select_related('fild')
                except UserProfile.DoesNotExist:
                    devices = Device.objects.none()
        else:
            devices = Device.objects.none()

        # Get all fields that have devices
        fields = Field.objects.filter(devices__in=devices).distinct()

        # Get device asets
        device_asets = devices.values_list('aset', flat=True).distinct()

        # Get selected device IDs
        selected_ids = []
        if value:
            if isinstance(value, (list, tuple)):
                selected_ids = [str(v) for v in value]
            else:
                selected_ids = [str(value)]

        # Build the HTML
        html = f'''
        <div class="device-selection-widget" data-field-name="{name}">
            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="device-asset-filter" class="form-label">Asset:</label>
                    <select id="device-asset-filter" class="form-select">
                        <option value="">All Assets</option>
        '''

        for device_aset in device_asets:
            html += f'<option value="{device_aset}">{device_aset}</option>'

        html += '''
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="device-name-filter" class="form-label">Device Name:</label>
                    <input type="text" id="device-name-filter" class="form-control"
                           placeholder="Search device name...">
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="clear-all-filters">
                            Clear All Filters
                        </button>
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-12">
                    <label class="form-label">Fields:</label>
                    <div class="field-filter-buttons">
        '''

        for field in fields:
            html += f'''
                        <button type="button" class="btn btn-outline-primary btn-sm me-2 mb-2 field-filter-btn"
                                data-field-id="{field.id}">{field.name}</button>
            '''

        html += '''
                        <button type="button" class="btn btn-outline-secondary btn-sm me-2 mb-2"
                                id="clear-field-filter">All Fields</button>
                    </div>
                </div>
            </div>

            <div class="table-responsive" id="device-table-container">
                <table class="table table-bordered table-hover" id="device-selection-table">
                    <thead class="table-light">
                        <tr>
                            <th>Field</th>
                            <th>Device</th>
                            <th>Asset</th>
                            <th>Select</th>
                        </tr>
                    </thead>
                    <tbody>
        '''

        for device in devices:
            checked = 'checked' if str(device.id) in selected_ids else ''
            html += f'''
                        <tr data-device-id="{device.id}" data-field-id="{device.fild.id}"
                            data-asset="{device.aset}">
                            <td>{device.fild.name}</td>
                            <td>{device.name}</td>
                            <td>{device.aset}</td>
                            <td>
                                <input type="checkbox" name="{name}" value="{device.id}"
                                       class="form-check-input device-checkbox" {checked}>
                            </td>
                        </tr>
            '''

        html += '''
                    </tbody>
                </table>
            </div>

            <div class="row mt-3">
                <div class="col-12">
                    <button type="button" class="btn btn-sm btn-outline-primary me-2" id="select-all-devices">
                        Select All
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="deselect-all-devices">
                        Deselect All
                    </button>
                </div>
            </div>
        </div>
        '''

        return html

    def value_from_datadict(self, data, files, name):
        print(name,data)
        return data.getlist(name)


class NotificationSettingsForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Use custom widget for device selection
        self.fields['devs'].widget = DeviceSelectionWidget(user=self.user)

    class Meta:
        model = NotificationSettings
        fields = ["rxif", "rxup", "rxwr", "rxdg", "devs", "mthd"]
        labels = {
            "rxif": "Info",
            "rxup": "Updates",
            "rxwr": "Warnings",
            "rxdg": "Danger",
            "devs": "Devices",
            "mthd": "Delivery Method",
        }
        widgets = {
            "rxif": forms.CheckboxInput(
                attrs={"type": "checkbox", "data-switch": "info"}
            ),
            "rxup": forms.CheckboxInput(
                attrs={"type": "checkbox", "data-switch": "primary"}
            ),
            "rxwr": forms.CheckboxInput(
                attrs={"type": "checkbox", "data-switch": "warning"}
            ),
            "rxdg": forms.CheckboxInput(
                attrs={"type": "checkbox", "data-switch": "danger"}
            ),
            "mthd": forms.Select(
                attrs={
                    "class": "form-control",
                    "data-toggle": "form-select",
                }
            ),
        }
