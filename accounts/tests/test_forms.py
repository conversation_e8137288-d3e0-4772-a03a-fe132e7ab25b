from django.test import TestCase, tag
from accounts.forms import UserAndProfileForm
from django.contrib.auth.models import User
from device_manager.models import Device
from fields.models import Field
from django import forms
from django.http import QueryDict
from unittest.mock import patch


class BaseUserFormTest(TestCase):
    """Base class for user form tests with common setup"""
    def setUp(self):
        qdata = QueryDict('', mutable=True)
        qdata.update({
            'usrn': 'testuser',
            'fnam': 'Test',
            'lnam': 'User',
            'emal': '<EMAIL>',
            'role': 'User',
            'phon': '********',
            'titl': 'Test Title',
            'orgn': 'Test Organization',
            'pwrd': 'somepassword',
        })

        self.valid_form_data = qdata


@tag('integration')
class UserFormIntegrationTest(BaseUserFormTest):
    """Tests for user forms without using mocks"""

    def test_valid_form(self):
        """Test that the form is valid with correct data"""
        form = UserAndProfileForm(data=self.valid_form_data, is_create=True)
        self.assertTrue(form.is_valid())

    def test_blank_username(self):
        """Test that the form is invalid when username is blank"""
        invalid_data = self.valid_form_data.copy()
        invalid_data['usrn'] = ''
        form = UserAndProfileForm(data=invalid_data, is_create=True)
        self.assertFalse(form.is_valid())
        self.assertIn('usrn', form.errors)

    def test_blank_password(self):
        """Test that the form is invalid when password is blank"""
        invalid_data = self.valid_form_data.copy()
        invalid_data['pwrd'] = ''
        form = UserAndProfileForm(data=invalid_data, is_create=True)
        self.assertFalse(form.is_valid())
        self.assertIn('pwrd', form.errors)

    def test_duplicate_username(self):
        """Test that the form is invalid when username already exists"""
        # Create a user with the same username
        User.objects.create_user(
            username='testuser',
            password='password',
            email='<EMAIL>'
        )

        form = UserAndProfileForm(data=self.valid_form_data, is_create=True)
        self.assertFalse(form.is_valid())
        self.assertIn('usrn', form.errors)

    def test_invalid_email_format(self):
        """Test that the form is invalid when email format is incorrect"""
        invalid_data = self.valid_form_data.copy()
        invalid_data['emal'] = 'not-an-email'
        form = UserAndProfileForm(data=invalid_data, is_create=True)
        self.assertFalse(form.is_valid())
        self.assertIn('emal', form.errors)

    def test_invalid_phone_format(self):
        """Test that the form is invalid when phone format is incorrect"""
        invalid_data = self.valid_form_data.copy()
        invalid_data['phon'] = '123456'  # Not 8 digits
        form = UserAndProfileForm(data=invalid_data, is_create=True)
        # Note: Phone validation happens at model level, form may still be valid
        # This test verifies the form accepts the data
        self.assertTrue(form.is_valid())

    def test_blank_required_fields(self):
        """Test that the form is invalid when required fields are blank"""
        required_fields = ['fnam', 'lnam', 'emal', 'titl', 'orgn']

        for field in required_fields:
            invalid_data = self.valid_form_data.copy()
            invalid_data[field] = ''
            form = UserAndProfileForm(data=invalid_data, is_create=True)
            self.assertFalse(form.is_valid())
            self.assertIn(field, form.errors)

    def test_non_create_mode(self):
        """Test form in non-create mode (update mode)"""
        # In non-create mode, duplicate username check should be skipped
        User.objects.create_user(
            username='testuser',
            password='password',
            email='<EMAIL>'
        )

        form = UserAndProfileForm(data=self.valid_form_data, is_create=False)
        self.assertTrue(form.is_valid())

    def test_device_assignment(self):
        """Test that devices can be assigned to user"""
        # Create test field first
        field = Field.objects.create(
            name="Test Field",
            cord=[],
            colr="#FF0000",
            covr=0.0,
            loca="Test Location"
        )

        # Create test devices with required fields
        device1 = Device.objects.create(
            name="Test Device 1",
            euid="********90ABCDEF",
            type="Whiskers Node V1",
            fild=field,
            offp="1"
        )
        device2 = Device.objects.create(
            name="Test Device 2",
            euid="FEDCBA0987654321",
            type="Whiskers Node V1",
            fild=field,
            offp="2"
        )

        form_data = self.valid_form_data.copy()
        for device in [device1.id, device2.id]:  # یا مقدار واقعی
            form_data.update({'devs': device})

        form = UserAndProfileForm(data=form_data, is_create=True)
        self.assertTrue(form.is_valid())
        self.assertEqual(list(form.cleaned_data['devs']), [device1, device2])

    def test_role_choices(self):
        """Test that valid role choices are accepted"""
        valid_roles = ['Admin', 'Owner', 'User']

        for role in valid_roles:
            form_data = self.valid_form_data.copy()
            form_data['role'] = role
            form = UserAndProfileForm(data=form_data, is_create=True)
            self.assertTrue(form.is_valid())
            self.assertEqual(form.cleaned_data['role'], role)


@tag('unit')
class UserFormUnitTest(TestCase):
    """Unit tests for user forms using mocks to isolate functionality"""

    def setUp(self):
        qdata = QueryDict('', mutable=True)
        qdata.update({
            'usrn': 'testuser',
            'fnam': 'Test',
            'lnam': 'User',
            'emal': '<EMAIL>',
            'role': 'User',
            'phon': '********',
            'titl': 'Test Title',
            'orgn': 'Test Organization',
            'pwrd': 'StrongPass123!',
            'devs': []
        })

        self.valid_form_data = qdata

    @patch('accounts.forms.UserAndProfileForm.clean_emal')
    def test_email_validation_error_handling(self, mock_clean_emal):
        """Test form handling of email validation error"""
        # Configure the mock to raise a validation error
        mock_clean_emal.side_effect = forms.ValidationError("Invalid email format")

        # Create and validate the form
        form = UserAndProfileForm(data=self.valid_form_data, is_create=True)
        is_valid = form.is_valid()

        # Form should be invalid
        self.assertFalse(is_valid)
        self.assertIn('emal', form.errors)
        self.assertEqual(form.errors['emal'][0], "Invalid email format")

    @patch('accounts.forms.UserAndProfileForm.clean_usrn')
    def test_username_validation_error_handling(self, mock_clean_usrn):
        """Test form handling of username validation error"""
        # Configure the mock to raise a validation error
        mock_clean_usrn.side_effect = forms.ValidationError("Username already exists")

        # Create and validate the form
        form = UserAndProfileForm(data=self.valid_form_data, is_create=True)
        is_valid = form.is_valid()

        # Form should be invalid
        self.assertFalse(is_valid)
        self.assertIn('usrn', form.errors)
        self.assertEqual(form.errors['usrn'][0], "Username already exists")

    def test_form_initialization_with_create_flag(self):
        """Test form initialization with is_create flag"""
        form = UserAndProfileForm(data=self.valid_form_data, is_create=True)
        self.assertTrue(form.is_create)

        form = UserAndProfileForm(data=self.valid_form_data, is_create=False)
        self.assertFalse(form.is_create)

    def test_form_field_attributes(self):
        """Test that form fields have correct attributes"""
        form = UserAndProfileForm()

        # Check that devices field has correct widget attributes
        devs_field = form.fields['devs']
        self.assertEqual(devs_field.widget.attrs['class'], 'device-selection-widget')

        # Check that role field has correct choices
        role_field = form.fields['role']
        expected_choices = [('Admin', 'Admin'), ('Owner', 'Owner'), ('User', 'User')]
        self.assertEqual(role_field.choices, expected_choices)

    def test_clean_emal_with_invalid_email(self):
        """Test email cleaning with invalid email"""
        form_data = self.valid_form_data.copy()
        form_data['emal'] = 'invalid-email'

        form = UserAndProfileForm(data=form_data, is_create=True)
        self.assertFalse(form.is_valid())
        self.assertIn('emal', form.errors)
        # Check for either Django's default message or our custom message
        error_message = str(form.errors['emal'])
        self.assertTrue(
            'Enter a valid email address' in error_message or
            'Invalid email address' in error_message
        )

    def test_clean_usrn_with_existing_username_create_mode(self):
        """Test username cleaning with existing username in create mode"""
        # Create a user first
        User.objects.create_user(username='existinguser', password='pass')

        form_data = self.valid_form_data.copy()
        form_data['usrn'] = 'existinguser'

        form = UserAndProfileForm(data=form_data, is_create=True)
        self.assertFalse(form.is_valid())
        self.assertIn('usrn', form.errors)

    def test_clean_usrn_with_existing_username_update_mode(self):
        """Test username cleaning with existing username in update mode"""
        # Create a user first
        existing_user = User.objects.create_user(username='existinguser', password='pass')

        form_data = self.valid_form_data.copy()
        form_data['usrn'] = 'existinguser'

        # In update mode, should not validate against existing usernames
        form = UserAndProfileForm(data=form_data, is_create=False,user=existing_user)
        self.assertTrue(form.is_valid())

    def test_form_with_empty_devices(self):
        """Test form with empty devices list"""
        form_data = self.valid_form_data.copy()
        form_data.update({
            'devs': []
        })

        form = UserAndProfileForm(data=form_data, is_create=True)
        self.assertTrue(form.is_valid())
        self.assertEqual(list(form.cleaned_data['devs']), [])

    def test_form_field_labels_and_help_texts(self):
        """Test that form fields have correct labels and help texts"""
        form = UserAndProfileForm()

        # Check some key field labels
        self.assertEqual(form.fields['usrn'].label, 'Username')
        self.assertEqual(form.fields['fnam'].label, 'First Name')
        self.assertEqual(form.fields['lnam'].label, 'Last Name')
        self.assertEqual(form.fields['emal'].label, 'Email')
        self.assertEqual(form.fields['role'].label, 'Role')
        self.assertEqual(form.fields['phon'].label, 'Phone Number')  # Fixed label
        self.assertEqual(form.fields['titl'].label, 'Title')
        self.assertEqual(form.fields['orgn'].label, 'Organization')
        self.assertEqual(form.fields['pwrd'].label, 'Password')
        self.assertEqual(form.fields['devs'].label, 'Devices')

    def test_form_widget_attributes(self):
        """Test that form widgets have correct attributes"""
        form = UserAndProfileForm()

        # Check password field widget
        password_widget = form.fields['pwrd'].widget
        self.assertEqual(password_widget.attrs['class'], 'form-control')

        # Check devices field widget
        devices_widget = form.fields['devs'].widget
        self.assertIn('device-selection-widget', devices_widget.attrs['class'])